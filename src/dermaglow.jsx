import React, { useState, useRef, useEffect } from 'react';
import { Camera, Upload, User, Calendar, Clock, Shield, ChevronRight, Star, Users, Award, Phone, Mail, MapPin, Menu, X } from 'lucide-react';

const DermaglowPlatform = () => {
  const [activeTab, setActiveTab] = useState('home');
  const [userType, setUserType] = useState(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [consultationForm, setConsultationForm] = useState({
    symptoms: '',
    duration: '',
    location: '',
    severity: ''
  });
  const [cameraActive, setCameraActive] = useState(false);
  const [cameraFeedback, setCameraFeedback] = useState([]);
  const [isOptimalPose, setIsOptimalPose] = useState(false);
  const [simpleMode, setSimpleMode] = useState(false);
  const videoRef = React.useRef(null);
  const canvasRef = React.useRef(null);
  const streamRef = React.useRef(null);
  let analyzeFaceInterval;

  // Effect to ensure video stream stays connected - only run when camera state changes, not feedback
  useEffect(() => {
    if (cameraActive && streamRef.current && videoRef.current) {
      const video = videoRef.current;
      if (!video.srcObject) {
        console.log("🔧 useEffect: Restoring stream to video element");
        video.srcObject = streamRef.current;
      }
    }
  }, [cameraActive]); // Only re-run when camera active state changes, not feedback

  const commonConditions = [
    { name: 'Acne & Breakouts', icon: '🔴', description: 'Pimples, blackheads, and skin inflammation' },
    { name: 'Eczema', icon: '🟡', description: 'Dry, itchy, and inflamed skin patches' },
    { name: 'Hyperpigmentation', icon: '🟤', description: 'Dark spots and uneven skin tone' },
    { name: 'Fungal Infections', icon: '🟢', description: 'Ringworm, athlete\'s foot, and other fungal issues' },
    { name: 'Keloids & Scarring', icon: '🔵', description: 'Raised scars and keloid formations' },
    { name: 'Contact Dermatitis', icon: '🟠', description: 'Allergic reactions and skin irritation' }
  ];

  const doctors = [
    { name: 'Dr. Adebayo Ogundimu', specialty: 'Pediatric Dermatology', rating: 4.9, experience: '12 years', image: '👨🏿‍⚕️' },
    { name: 'Dr. Chioma Okwu', specialty: 'Cosmetic Dermatology', rating: 4.8, experience: '8 years', image: '👩🏿‍⚕️' },
    { name: 'Dr. Ibrahim Yusuf', specialty: 'Medical Dermatology', rating: 4.9, experience: '15 years', image: '👨🏿‍⚕️' }
  ];

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => setSelectedImage(e.target.result);
      reader.readAsDataURL(file);
    }
  };

  const handleFormSubmit = (e) => {
    e.preventDefault();
    alert('Consultation request submitted! A dermatologist will review your case within 24 hours.');
  };

  const activateCamera = () => {
    console.log("🎥 Activating guided camera mode");
    setSimpleMode(false);
    setCameraActive(true);
    setCameraFeedback(["Initializing camera..."]);

    setTimeout(() => {
      console.log("🎯 Video ref current:", videoRef.current);
      if (videoRef.current) {
        navigator.mediaDevices.getUserMedia({ video: true })
          .then(stream => {
            console.log("📹 Camera stream obtained", stream);
            console.log("🎯 Setting srcObject on video element:", videoRef.current);

            // Store stream in ref for persistence
            streamRef.current = stream;
            videoRef.current.srcObject = stream;
            console.log("✅ srcObject set, now:", videoRef.current.srcObject);

            // Force video to load
            videoRef.current.load();

            // Set up multiple event listeners to ensure we catch when video is ready
            const startAnalysis = () => {
              console.log("🔍 Starting face analysis");
              setCameraFeedback(["Camera ready - analyzing face position..."]);
              // Start analysis after a short delay
              setTimeout(() => {
                console.log("⏰ Setting up analysis interval");
                analyzeFaceInterval = setInterval(analyzeFacePosition, 1500);
              }, 1000);
            };

            // Try multiple events to ensure we start analysis
            videoRef.current.onloadedmetadata = () => {
              console.log("📊 Video metadata loaded");
              startAnalysis();
            };
            videoRef.current.oncanplay = () => {
              console.log("▶️ Video can play");
              startAnalysis();
            };
            videoRef.current.onloadeddata = () => {
              console.log("📊 Video data loaded");
              startAnalysis();
            };

            // Fallback: start analysis after a reasonable time even if events don't fire
            setTimeout(() => {
              console.log("⏱️ Fallback timer triggered");
              if (cameraFeedback.includes("Initializing camera...")) {
                console.log("🔄 Starting analysis via fallback");
                startAnalysis();
              }
            }, 3000);

            // Try to play the video
            videoRef.current.play().catch(err => {
              console.log("⚠️ Video play failed:", err);
            });
          })
          .catch(err => {
            console.error("❌ Error accessing camera:", err);
            setCameraFeedback(["Camera access denied"]);
            setCameraActive(false);
          });
      }
    }, 100);
  };

  const activateSimpleCamera = () => {
    setSimpleMode(true);
    setCameraActive(true);
    setCameraFeedback(["Camera ready - click capture when ready"]);

    setTimeout(() => {
      if (videoRef.current) {
        navigator.mediaDevices.getUserMedia({ video: true })
          .then(stream => {
            streamRef.current = stream;
            videoRef.current.srcObject = stream;
            videoRef.current.play();
          })
          .catch(err => {
            console.error("Error accessing camera:", err);
            setCameraFeedback(["Camera access denied"]);
            setCameraActive(false);
          });
      }
    }, 100);
  };

  // New function that only analyzes face position without sending the full image
  const analyzeFacePosition = () => {
    console.log("🔍 analyzeFacePosition called");

    // Skip analysis if in simple mode
    if (simpleMode) {
      console.log("⏭️ Skipping analysis - simple mode");
      return;
    }

    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;

      console.log("🎯 Video element in analysis:", video);
      console.log("📹 Video state:", {
        srcObject: !!video.srcObject,
        readyState: video.readyState,
        videoWidth: video.videoWidth,
        videoHeight: video.videoHeight,
        actualSrcObject: video.srcObject
      });

      // Check if video is ready - restore stream if lost but don't trigger feedback update
      if (!video.srcObject && streamRef.current) {
        console.log("🔄 Silently restoring lost stream to video element");
        video.srcObject = streamRef.current;
        return; // Skip this analysis cycle, let it work on the next one
      }

      if (!video.srcObject) {
        console.log("⏳ No srcObject - video not ready yet");
        setCameraFeedback(["Waiting for camera to initialize..."]);
        return;
      }

      if (video.readyState < 2) {
        console.log("⏳ ReadyState < 2 - video not ready yet, readyState:", video.readyState);
        setCameraFeedback(["Waiting for camera to initialize..."]);
        return;
      }

      console.log("✅ Video is ready! Proceeding with analysis");

      // Use fallback dimensions if video dimensions aren't available yet
      const width = video.videoWidth || 640;
      const height = video.videoHeight || 480;

      console.log("📐 Using dimensions:", { width, height });

      const context = canvas.getContext('2d');

      canvas.width = width;
      canvas.height = height;

      try {
        context.drawImage(video, 0, 0, width, height);

        // Create a smaller thumbnail for analysis only
        const thumbnailCanvas = document.createElement('canvas');
        const thumbnailContext = thumbnailCanvas.getContext('2d');
        thumbnailCanvas.width = 320; // Smaller size for analysis
        thumbnailCanvas.height = 240;
        thumbnailContext.drawImage(video, 0, 0, thumbnailCanvas.width, thumbnailCanvas.height);

        const thumbnailData = thumbnailCanvas.toDataURL('image/jpeg', 0.7);

        console.log("📤 Sending image data to backend, size:", thumbnailData.length);

        // Send only the thumbnail for position analysis
        fetch('http://localhost:5000/analyze-face', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            image: thumbnailData,
            analysisOnly: true // Flag to indicate we're just analyzing position
          }),
        })
        .then(response => {
          console.log("📥 Response received:", response.status);
          return response.json();
        })
        .then(data => {
          console.log("📊 Backend response:", data);
          if (data.success) {
            // Use callback to prevent unnecessary re-renders
            setCameraFeedback(prev => {
              // Only update if feedback actually changed
              if (JSON.stringify(prev) !== JSON.stringify(data.feedback)) {
                return data.feedback;
              }
              return prev;
            });
            setIsOptimalPose(data.isOptimal);
          } else {
            setCameraFeedback([data.message || 'Analysis error - you can still capture manually']);
          }
        })
        .catch(error => {
          console.error('❌ Error analyzing face:', error);
          setCameraFeedback(['Connection error - you can still capture manually']);
        });
      } catch (error) {
        console.error('Error drawing video to canvas:', error);
        setCameraFeedback(["Camera ready - you can capture manually"]);
      }
    }
  };

  // Separate function for manual capture - now works even without optimal pose
  const captureImage = () => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');
      
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      context.drawImage(video, 0, 0, canvas.width, canvas.height);
      
      const imageData = canvas.toDataURL('image/jpeg', 0.9); // Higher quality for the actual capture
      setSelectedImage(imageData);
      stopCamera();
    }
  };

  const stopCamera = () => {
    // Stop tracks from stored stream
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    // Also stop from video element if it has a stream
    if (videoRef.current && videoRef.current.srcObject) {
      videoRef.current.srcObject.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }

    if (analyzeFaceInterval) {
      clearInterval(analyzeFaceInterval);
    }
    setCameraActive(false);
    setCameraFeedback([]);
    setSimpleMode(false);
  };

  const Navigation = () => (
    <nav className="bg-white shadow-lg sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <div className="text-2xl font-bold text-emerald-600">Dermaglow</div>
            <div className="ml-2 text-sm text-gray-500">Nigeria</div>
          </div>
          
          <div className="hidden md:flex space-x-8">
            {['home', 'consult', 'conditions', 'doctors', 'about'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-3 py-2 text-sm font-medium capitalize ${
                  activeTab === tab ? 'text-emerald-600 border-b-2 border-emerald-600' : 'text-gray-700 hover:text-emerald-600'
                }`}
              >
                {tab}
              </button>
            ))}
          </div>

          <div className="hidden md:flex items-center space-x-4">
            <button className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors">
              Login
            </button>
          </div>

          <button
            className="md:hidden"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          >
            {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {mobileMenuOpen && (
        <div className="md:hidden bg-white border-t">
          <div className="px-4 py-2 space-y-2">
            {['home', 'consult', 'conditions', 'doctors', 'about'].map((tab) => (
              <button
                key={tab}
                onClick={() => {
                  setActiveTab(tab);
                  setMobileMenuOpen(false);
                }}
                className="block w-full text-left px-3 py-2 text-sm font-medium capitalize text-gray-700 hover:text-emerald-600"
              >
                {tab}
              </button>
            ))}
            <button className="w-full bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors">
              Login
            </button>
          </div>
        </div>
      )}
    </nav>
  );

  const HomePage = () => (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 to-teal-50">
      {/* Hero Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
              Quality Skin Care,
              <span className="text-emerald-600"> Anywhere in Nigeria</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Connect with certified dermatologists remotely. Get expert diagnosis and treatment for all skin conditions from the comfort of your home.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 mb-8">
              <button
                onClick={() => setActiveTab('consult')}
                className="bg-emerald-600 text-white px-8 py-4 rounded-lg hover:bg-emerald-700 transition-colors flex items-center justify-center space-x-2 text-lg font-semibold"
              >
                <Camera size={24} />
                <span>Start Consultation</span>
              </button>
              <button
                onClick={() => setActiveTab('doctors')}
                className="border-2 border-emerald-600 text-emerald-600 px-8 py-4 rounded-lg hover:bg-emerald-50 transition-colors flex items-center justify-center space-x-2 text-lg font-semibold"
              >
                <Users size={24} />
                <span>Meet Our Doctors</span>
              </button>
            </div>
          </div>
          
          <div className="relative">
            <div className="bg-white rounded-2xl shadow-2xl p-8 transform rotate-3 hover:rotate-0 transition-transform duration-300">
              <div className="aspect-square bg-gradient-to-br from-emerald-100 to-teal-100 rounded-xl flex items-center justify-center text-6xl">
                <img src="/img/IMG_1815.JPG" alt="" />
              </div>
              <div className="mt-4 text-center">
                <h3 className="font-semibold text-gray-900">Professional Care</h3>
                <p className="text-gray-600">Licensed Nigerian Dermatologists</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">Why Choose Dermaglow?</h2>
        <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {[
            { icon: <Shield className="text-emerald-600" size={32} />, title: 'Secure & Private', desc: 'End-to-end encrypted consultations' },
            { icon: <Clock className="text-emerald-600" size={32} />, title: '24hr Response', desc: 'Quick diagnosis within 24 hours' },
            { icon: <Award className="text-emerald-600" size={32} />, title: 'Licensed Doctors', desc: 'Certified Nigerian dermatologists' },
            { icon: <Phone className="text-emerald-600" size={32} />, title: 'Follow-up Care', desc: 'Ongoing support and monitoring' }
          ].map((feature, index) => (
            <div key={index} className="text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="flex justify-center mb-4">{feature.icon}</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
              <p className="text-gray-600">{feature.desc}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Common Conditions Preview */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">Common Skin Conditions We Treat</h2>
        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {commonConditions.slice(0, 3).map((condition, index) => (
            <div key={index} className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="text-3xl mb-3">{condition.icon}</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">{condition.name}</h3>
              <p className="text-gray-600">{condition.description}</p>
            </div>
          ))}
        </div>
        <div className="text-center mt-8">
          <button
            onClick={() => setActiveTab('conditions')}
            className="text-emerald-600 hover:text-emerald-700 font-semibold flex items-center mx-auto space-x-2"
          >
            <span>View All Conditions</span>
            <ChevronRight size={20} />
          </button>
        </div>
      </div>
    </div>
  );

  const ConsultationPage = () => (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Start Your Consultation</h1>
          <p className="text-gray-600">Upload clear photos and describe your symptoms for accurate diagnosis</p>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-8">
          <form onSubmit={handleFormSubmit} className="space-y-6">
            {/* Image Upload Section */}
            <div className="border-2 border-dashed border-emerald-300 rounded-xl p-8 text-center">
              <div className="flex flex-col items-center space-y-4">
                {selectedImage ? (
                  <div className="relative">
                    <img src={selectedImage} alt="Uploaded skin condition" className="max-w-xs max-h-64 rounded-lg object-cover" />
                    <button
                      type="button"
                      onClick={() => setSelectedImage(null)}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm"
                    >
                      ×
                    </button>
                  </div>
                ) : cameraActive ? (
                  <div className="relative w-full max-w-md">
                    <video
                      ref={videoRef}
                      key={`video-${cameraActive}-${simpleMode}`}
                      className="w-full rounded-lg border border-emerald-300"
                      muted
                      autoPlay
                      playsInline
                    ></video>
                    <canvas ref={canvasRef} className="hidden"></canvas>
                    
                    {/* Feedback overlay */}
                    <div className="absolute top-4 left-0 right-0 flex flex-col items-center">
                      {cameraFeedback.map((feedback, i) => (
                        <div key={i} className={`bg-black bg-opacity-70 text-white px-4 py-2 rounded-lg mb-2 ${
                          feedback.includes("Too") || feedback.includes("Turn") || feedback.includes("Tilt")
                            ? "text-yellow-300"
                            : feedback.includes("Perfect")
                              ? "text-emerald-300"
                              : feedback.includes("Connection error") || feedback.includes("No face detected")
                                ? "text-orange-300"
                                : feedback.includes("Initializing") || feedback.includes("Waiting") || feedback.includes("ready")
                                  ? "text-blue-300"
                                  : ""
                        }`}>
                          {feedback}
                        </div>
                      ))}

                      {cameraFeedback.length === 0 && (
                        <div className="bg-black bg-opacity-70 text-white px-4 py-2 rounded-lg mb-2">
                          Analyzing... Click the camera button to capture at any time
                        </div>
                      )}
                    </div>
                    
                    <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-4">
                      <button
                        type="button"
                        onClick={captureImage}
                        className={`${
                          isOptimalPose 
                            ? "bg-emerald-500 hover:bg-emerald-600" 
                            : "bg-yellow-500 hover:bg-yellow-600"
                        } text-white rounded-full p-3`}
                      >
                        <Camera size={24} />
                      </button>
                      
                      <button
                        type="button"
                        onClick={stopCamera}
                        className="bg-red-500 hover:bg-red-600 text-white rounded-full p-3"
                      >
                        <X size={24} />
                      </button>
                    </div>
                    
                    {isOptimalPose && (
                      <div className="absolute top-0 left-0 right-0 bottom-0 border-4 border-emerald-500 rounded-lg pointer-events-none"></div>
                    )}
                  </div>
                ) : (
                  <div className="text-6xl text-emerald-300 mb-4">📸</div>
                )}
                
                {!cameraActive && !selectedImage && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Upload Skin Photos</h3>
                    <p className="text-gray-600 mb-4">For best results, take photos in good lighting from multiple angles</p>
                    
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                      <button
                        type="button"
                        onClick={activateCamera}
                        className="bg-emerald-600 text-white px-6 py-3 rounded-lg hover:bg-emerald-700 transition-colors cursor-pointer flex items-center space-x-2"
                      >
                        <Camera size={20} />
                        <span>Take Photo with Guidance</span>
                      </button>

                      <button
                        type="button"
                        onClick={activateSimpleCamera}
                        className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors cursor-pointer flex items-center space-x-2"
                      >
                        <Camera size={20} />
                        <span>Simple Camera</span>
                      </button>

                      <label className="border-2 border-emerald-600 text-emerald-600 px-6 py-3 rounded-lg hover:bg-emerald-50 transition-colors cursor-pointer flex items-center space-x-2">
                        <Upload size={20} />
                        <span>Upload from Gallery</span>
                        <input type="file" accept="image/*" onChange={handleImageUpload} className="hidden" />
                      </label>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Symptoms Form */}
            <div className="grid sm:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Describe Your Symptoms</label>
                <textarea
                  value={consultationForm.symptoms}
                  onChange={(e) => setConsultationForm({...consultationForm, symptoms: e.target.value})}
                  rows="4"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  placeholder="Describe what you're experiencing, including itching, pain, changes in appearance, etc."
                />
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">How long have you had this condition?</label>
                  <select
                    value={consultationForm.duration}
                    onChange={(e) => setConsultationForm({...consultationForm, duration: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="">Select duration</option>
                    <option value="less-than-week">Less than a week</option>
                    <option value="1-2-weeks">1-2 weeks</option>
                    <option value="1-month">About a month</option>
                    <option value="several-months">Several months</option>
                    <option value="over-year">Over a year</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Location on Body</label>
                  <input
                    type="text"
                    value={consultationForm.location}
                    onChange={(e) => setConsultationForm({...consultationForm, location: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    placeholder="e.g., face, arms, back, legs"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Severity Level</label>
                  <select
                    value={consultationForm.severity}
                    onChange={(e) => setConsultationForm({...consultationForm, severity: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="">Select severity</option>
                    <option value="mild">Mild - Minor discomfort</option>
                    <option value="moderate">Moderate - Noticeable impact</option>
                    <option value="severe">Severe - Significant impact on daily life</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="bg-emerald-50 p-6 rounded-lg">
              <h4 className="font-semibold text-emerald-800 mb-2">💡 Tips for Better Photos:</h4>
              <ul className="text-emerald-700 space-y-1 text-sm">
                <li>• Use natural lighting when possible</li>
                <li>• Take photos from different angles</li>
                <li>• Ensure the affected area is clearly visible</li>
                <li>• Include surrounding healthy skin for comparison</li>
              </ul>
            </div>

            <button
              type="submit"
              className="w-full bg-emerald-600 text-white py-4 rounded-lg hover:bg-emerald-700 transition-colors font-semibold text-lg"
            >
              Submit Consultation Request
            </button>
          </form>
        </div>
      </div>
    </div>
  );

  const ConditionsPage = () => (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Skin Conditions We Treat</h1>
          <p className="text-gray-600">Common dermatological conditions affecting Nigerian skin</p>
        </div>

        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {commonConditions.map((condition, index) => (
            <div key={index} className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
              <div className="text-4xl mb-4">{condition.icon}</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">{condition.name}</h3>
              <p className="text-gray-600 mb-4">{condition.description}</p>
              <button
                onClick={() => setActiveTab('consult')}
                className="text-emerald-600 hover:text-emerald-700 font-semibold flex items-center space-x-2"
              >
                <span>Get Treatment</span>
                <ChevronRight size={16} />
              </button>
            </div>
          ))}
        </div>

        <div className="mt-12 bg-white rounded-xl shadow-lg p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Don't See Your Condition?</h2>
          <p className="text-gray-600 mb-6">
            Our dermatologists treat a wide range of skin, hair, and nail conditions. If you don't see your specific condition listed, 
            don't worry - our experts are equipped to help with virtually any dermatological concern.
          </p>
          <button
            onClick={() => setActiveTab('consult')}
            className="bg-emerald-600 text-white px-6 py-3 rounded-lg hover:bg-emerald-700 transition-colors"
          >
            Start Your Consultation
          </button>
        </div>
      </div>
    </div>
  );

  const DoctorsPage = () => (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Our Dermatologists</h1>
          <p className="text-gray-600">Licensed professionals specializing in Nigerian skin health</p>
        </div>

        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {doctors.map((doctor, index) => (
            <div key={index} className="bg-white rounded-xl shadow-lg p-6 text-center hover:shadow-xl transition-shadow">
              <div className="text-6xl mb-4">{doctor.image}</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">{doctor.name}</h3>
              <p className="text-emerald-600 font-medium mb-2">{doctor.specialty}</p>
              <div className="flex items-center justify-center space-x-2 mb-2">
                <div className="flex text-yellow-400">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} size={16} fill="currentColor" />
                  ))}
                </div>
                <span className="text-gray-600 text-sm">{doctor.rating}</span>
              </div>
              <p className="text-gray-600 text-sm mb-4">{doctor.experience} experience</p>
              <button
                onClick={() => setActiveTab('consult')}
                className="w-full bg-emerald-600 text-white py-2 rounded-lg hover:bg-emerald-700 transition-colors"
              >
                Book Consultation
              </button>
            </div>
          ))}
        </div>

        <div className="mt-12 bg-white rounded-xl shadow-lg p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Why Our Doctors?</h2>
          <div className="grid sm:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">🏥 Nigerian Medical Expertise</h4>
              <p className="text-gray-600">All our dermatologists are licensed to practice in Nigeria and understand local skin conditions and climate factors.</p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">🎓 Specialized Training</h4>
              <p className="text-gray-600">Each doctor has specialized training in dermatology with experience treating diverse skin types and conditions.</p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">⏰ Quick Response Times</h4>
              <p className="text-gray-600">Most consultations receive a response within 24 hours, with urgent cases prioritized.</p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">🔄 Ongoing Care</h4>
              <p className="text-gray-600">Follow-up consultations and treatment monitoring to ensure the best outcomes for your skin health.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const AboutPage = () => (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">About Dermaglow</h1>
          <p className="text-xl text-gray-600">Making quality dermatology care accessible across Nigeria</p>
        </div>

        <div className="space-y-8">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Our Mission</h2>
            <p className="text-gray-600 leading-relaxed">
              Dermaglow was created to bridge the gap in dermatological care across Nigeria. We recognize that access to specialized 
              skin care can be challenging, especially for students and young adults. Our platform connects you with licensed Nigerian 
              dermatologists who understand local skin conditions, climate factors, and cultural considerations.
            </p>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">How It Works</h2>
            <div className="grid sm:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="bg-emerald-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <Camera className="text-emerald-600" size={24} />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">1. Upload Photos</h3>
                <p className="text-gray-600 text-sm">Take clear photos of your skin condition using our guided upload process</p>
              </div>
              <div className="text-center">
                <div className="bg-emerald-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <User className="text-emerald-600" size={24} />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">2. Doctor Review</h3>
                <p className="text-gray-600 text-sm">A licensed dermatologist reviews your case and provides diagnosis</p>
              </div>
              <div className="text-center">
                <div className="bg-emerald-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <Calendar className="text-emerald-600" size={24} />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">3. Follow-up Care</h3>
                <p className="text-gray-600 text-sm">Receive treatment recommendations and ongoing support</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Contact Us</h2>
            <div className="grid sm:grid-cols-2 gap-6">
              <div className="flex items-center space-x-3">
                <Mail className="text-emerald-600" size={20} />
                <span className="text-gray-600"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="text-emerald-600" size={20} />
                <span className="text-gray-600">+234 (0) 800 DERMA-GLOW</span>
              </div>
              <div className="flex items-center space-x-3">
                <MapPin className="text-emerald-600" size={20} />
                <span className="text-gray-600">Lagos, Nigeria</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      
      {activeTab === 'home' && <HomePage />}
      {activeTab === 'consult' && <ConsultationPage />}
      {activeTab === 'conditions' && <ConditionsPage />}
      {activeTab === 'doctors' && <DoctorsPage />}
      {activeTab === 'about' && <AboutPage />}

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-8">
            <div>
              <div className="text-2xl font-bold text-emerald-400 mb-4">Dermaglow</div>
              <p className="text-gray-400">
                Quality dermatology care for all Nigerians, accessible from anywhere.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Services</h4>
              <ul className="space-y-2 text-gray-400">
                <li>Online Consultations</li>
                <li>Skin Condition Diagnosis</li>
                <li>Treatment Plans</li>
                <li>Follow-up Care</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li>Help Center</li>
                <li>Contact Us</li>
                <li>Privacy Policy</li>
                <li>Terms of Service</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Connect</h4>
              <p className="text-gray-400 mb-2"><EMAIL></p>
              <p className="text-gray-400">+234 (0) 800 DERMA-GLOW</p>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2025 Dermaglow. All rights reserved. | Improving Nigerian skin health through technology.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default DermaglowPlatform;
