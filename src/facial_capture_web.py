import cv2
import face_recognition
import numpy as np
import base64
import json
from flask import Flask, request, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

def analyze_lighting(face_image):
    gray = cv2.cvtColor(face_image, cv2.COLOR_BGR2GRAY)
    brightness = np.mean(gray)
    contrast = np.std(gray)
    return brightness, contrast

def analyze_face_angle(face_landmarks):
    left_eye = np.mean(face_landmarks['left_eye'], axis=0)
    right_eye = np.mean(face_landmarks['right_eye'], axis=0)
    nose_tip = face_landmarks['nose_tip'][0]
    
    eye_line = right_eye - left_eye
    horizontal_angle = np.arctan2(eye_line[1], eye_line[0]) * 180 / np.pi
    
    eye_center = (left_eye + right_eye) / 2
    vertical_angle = np.arctan2(nose_tip[1] - eye_center[1], 
                               nose_tip[0] - eye_center[0]) * 180 / np.pi
    
    return horizontal_angle, vertical_angle

@app.route('/analyze-face', methods=['POST'])
def analyze_face():
    # Get image data from request
    data = request.json
    image_data = data['image']
    analysis_only = data.get('analysisOnly', False)
    
    # Make sure we're handling the base64 data correctly
    if ',' in image_data:
        image_data = image_data.split(',')[1]
    
    # Decode base64 image
    try:
        image_bytes = base64.b64decode(image_data)
        np_arr = np.frombuffer(image_bytes, np.uint8)
        
        # Check if buffer is empty before decoding
        if np_arr.size == 0:
            return jsonify({
                'success': False,
                'message': 'Empty image data received',
                'feedback': ['Invalid image data']
            })
            
        frame = cv2.imdecode(np_arr, cv2.IMREAD_COLOR)
        
        if frame is None:
            return jsonify({
                'success': False,
                'message': 'Could not decode image',
                'feedback': ['Invalid image format']
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error processing image: {str(e)}',
            'feedback': ['Image processing error']
        })
    
    # Find faces in the frame
    face_locations = face_recognition.face_locations(frame)

    if not face_locations:
        return jsonify({
            'success': True,  # Changed to True so frontend doesn't block
            'isOptimal': False,
            'message': 'No face detected',
            'feedback': ['No face detected - center your face in the frame or capture manually']
        })
    
    # Get the largest face
    top, right, bottom, left = face_locations[0]
    face_image = frame[top:bottom, left:right]
    
    # Get face landmarks
    face_landmarks_list = face_recognition.face_landmarks(frame, face_locations)
    
    if not face_landmarks_list:
        return jsonify({
            'success': False,
            'message': 'Could not detect facial features',
            'feedback': ['Could not detect facial features - try better lighting']
        })
    
    # Analyze lighting
    brightness, contrast = analyze_lighting(face_image)
    
    # Analyze face angle
    h_angle, v_angle = analyze_face_angle(face_landmarks_list[0])
    
    # Ideal thresholds
    ideal_brightness = (100, 200)
    ideal_angle = (-10, 10)
    
    # Generate feedback
    feedback = []

    if brightness < ideal_brightness[0]:
        feedback.append("Too dark - improve lighting")
    elif brightness > ideal_brightness[1]:
        feedback.append("Too bright - reduce lighting")

    if h_angle < ideal_angle[0]:
        feedback.append("Turn face right slightly")
    elif h_angle > ideal_angle[1]:
        feedback.append("Turn face left slightly")

    if v_angle < ideal_angle[0]:
        feedback.append("Tilt face up slightly")
    elif v_angle > ideal_angle[1]:
        feedback.append("Tilt face down slightly")

    # Check if conditions are good
    is_optimal = len(feedback) == 0

    # If optimal, add a positive message
    if is_optimal:
        feedback.append("Perfect! Click the camera button to capture")

    return jsonify({
        'success': True,
        'isOptimal': is_optimal,
        'feedback': feedback,
        'metrics': {
            'brightness': float(brightness),
            'contrast': float(contrast),
            'horizontalAngle': float(h_angle),
            'verticalAngle': float(v_angle)
        }
    })

if __name__ == '__main__':
    app.run(debug=True, port=5000)
